{% comment %}
  Renders a comprehensive reviews section for tour products

  Accepts:
  - product: {Object} Product object
  - block: {Object} Block object (optional)

  Usage:
  {% render 'product-reviews', product: product %}
  {% render 'product-reviews', product: product, block: block %}
{% endcomment %}

{%- liquid
  assign show_rating_summary = true
  assign show_write_review_button = true
  assign reviews_heading = 'Customer Reviews'

  if block
    assign show_rating_summary = block.settings.show_rating_summary
    assign show_write_review_button = block.settings.show_write_review_button
  endif

  if section
    assign show_rating_summary = section.settings.show_rating_summary
    assign show_write_review_button = section.settings.show_write_review_button
    assign reviews_heading = section.settings.heading
  endif
-%}

<div class="product-reviews">
  <div class="product-reviews__container">
    <!-- Reviews Header -->
    <div class="reviews-header">
      <h3 class="reviews-title">{{ reviews_heading }}</h3>
      
      <!-- Rating Summary -->
      {%- if show_rating_summary and product.metafields.reviews.rating.value != blank -%}
        <div class="reviews-summary">
          {% liquid
            assign rating_decimal = 0
            assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
            if decimal >= 0.3 and decimal <= 0.7
              assign rating_decimal = 0.5
            elsif decimal > 0.7
              assign rating_decimal = 1
            endif
          %}
          <div class="rating-display">
            <div
              class="rating rating--large"
              role="img"
              aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
            >
              <span
                aria-hidden="true"
                class="rating-star"
                style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
              ></span>
            </div>
            <div class="rating-info">
              <span class="rating-value">{{ product.metafields.reviews.rating.value }}</span>
              <span class="rating-max">/ {{ product.metafields.reviews.rating.value.scale_max }}</span>
              <span class="rating-count">({{ product.metafields.reviews.rating_count }} {{ 'products.reviews.reviews' | t | default: 'reviews' }})</span>
            </div>
          </div>
        </div>
      {%- endif -%}
    </div>

    <!-- Reviews Widget -->
    <div class="reviews-widget">
      <!-- Judge.me Reviews Tab Widget -->
      <div class="jdgm-widget jdgm-review-widget" data-id="{{ product.id }}"></div>

      <!-- Judge.me Write Review Button -->
      {%- if show_write_review_button -%}
        <div class="reviews-actions">
          <div class="jdgm-widget jdgm-write-review-button" data-id="{{ product.id }}"></div>
        </div>
      {%- endif -%}
    </div>

    <!-- Custom Review Form (Always Available) -->
    <div class="custom-review-form">
      <div class="review-form-toggle">
        <button class="btn btn--primary" onclick="toggleReviewForm()">
          {{ 'products.reviews.write_review' | t | default: 'Write a Review' }}
        </button>
      </div>

      <div class="review-form-container" id="reviewForm" style="display: none;">
        <form class="review-form" onsubmit="submitReview(event)">
          <h4>{{ 'products.reviews.write_your_review' | t | default: 'Write Your Review' }}</h4>

          <!-- Star Rating Input -->
          <div class="rating-input">
            <label>{{ 'products.reviews.your_rating' | t | default: 'Your Rating' }}:</label>
            <div class="star-rating-input">
              <input type="radio" name="rating" value="5" id="star5">
              <label for="star5" class="star">★</label>
              <input type="radio" name="rating" value="4" id="star4">
              <label for="star4" class="star">★</label>
              <input type="radio" name="rating" value="3" id="star3">
              <label for="star3" class="star">★</label>
              <input type="radio" name="rating" value="2" id="star2">
              <label for="star2" class="star">★</label>
              <input type="radio" name="rating" value="1" id="star1">
              <label for="star1" class="star">★</label>
            </div>
          </div>

          <!-- Review Text -->
          <div class="form-group">
            <label for="reviewText">{{ 'products.reviews.your_review' | t | default: 'Your Review' }}:</label>
            <textarea id="reviewText" name="review" rows="4" required
                      placeholder="{{ 'products.reviews.review_placeholder' | t | default: 'Share your experience with this tour...' }}"></textarea>
          </div>

          <!-- Reviewer Name -->
          <div class="form-group">
            <label for="reviewerName">{{ 'products.reviews.your_name' | t | default: 'Your Name' }}:</label>
            <input type="text" id="reviewerName" name="name" required
                   placeholder="{{ 'products.reviews.name_placeholder' | t | default: 'Enter your name' }}">
          </div>

          <!-- Email -->
          <div class="form-group">
            <label for="reviewerEmail">{{ 'products.reviews.your_email' | t | default: 'Your Email' }}:</label>
            <input type="email" id="reviewerEmail" name="email" required
                   placeholder="{{ 'products.reviews.email_placeholder' | t | default: 'Enter your email' }}">
          </div>

          <input type="hidden" name="product_id" value="{{ product.id }}">
          <input type="hidden" name="product_title" value="{{ product.title }}">

          <div class="form-actions">
            <button type="submit" class="btn btn--primary">
              {{ 'products.reviews.submit_review' | t | default: 'Submit Review' }}
            </button>
            <button type="button" class="btn btn--secondary" onclick="toggleReviewForm()">
              {{ 'products.reviews.cancel' | t | default: 'Cancel' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Review Success Message -->
    <div class="review-success" id="reviewSuccess" style="display: none;">
      <div class="success-message">
        <h4>{{ 'products.reviews.thank_you' | t | default: 'Thank You!' }}</h4>
        <p>{{ 'products.reviews.review_submitted' | t | default: 'Your review has been submitted and will be published after moderation.' }}</p>
      </div>
    </div>
  </div>
</div>

<style>
.product-reviews {
  background: #ffffff;
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reviews-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-align: center;
}

.reviews-summary {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.rating--large .rating-star {
  font-size: 2rem;
  --letter-spacing: 0.2;
}

.rating-info {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  font-size: 1.1rem;
}

.rating-value {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.5rem;
}

.rating-max {
  color: #64748b;
  font-size: 1.2rem;
}

.rating-count {
  color: #64748b;
  font-size: 1rem;
  margin-left: 0.5rem;
}

.reviews-widget {
  margin-top: 2rem;
}

.reviews-actions {
  margin-top: 1.5rem;
  text-align: center;
}

.reviews-fallback {
  text-align: center;
  padding: 2rem;
}

.reviews-placeholder h4 {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.reviews-placeholder p {
  color: #94a3b8;
  margin-bottom: 1.5rem;
}

.reviews-write-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reviews-write-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Judge.me Widget Styling */
.jdgm-widget {
  margin: 1rem 0;
}

.jdgm-review-widget {
  border-radius: 12px;
  overflow: hidden;
}

/* Custom Review Form Styling */
.custom-review-form {
  margin-top: 2rem;
}

.review-form-toggle {
  text-align: center;
  margin-bottom: 1rem;
}

.review-form-container {
  background: #f8fafc;
  border-radius: 12px;
  padding: 2rem;
  margin-top: 1rem;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.review-form h4 {
  color: #1e293b;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Star Rating Input */
.rating-input {
  margin-bottom: 1.5rem;
}

.rating-input label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.star-rating-input {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 0.25rem;
}

.star-rating-input input[type="radio"] {
  display: none;
}

.star-rating-input .star {
  font-size: 2rem;
  color: #d1d5db;
  cursor: pointer;
  transition: color 0.2s ease;
  width: auto;
  padding: 0;
  border: none;
  background: none;
}

.star-rating-input .star:hover,
.star-rating-input .star:hover ~ .star,
.star-rating-input input[type="radio"]:checked ~ .star {
  color: #fbbf24;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.btn--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn--secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn--secondary:hover {
  background: #e5e7eb;
}

/* Success Message */
.review-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  margin-top: 1rem;
}

.success-message h4 {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.success-message p {
  margin: 0;
  opacity: 0.9;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .product-reviews {
    padding: 1.5rem;
    margin-top: 1.5rem;
  }
  
  .reviews-title {
    font-size: 1.5rem;
  }
  
  .rating-display {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .rating--large .rating-star {
    font-size: 1.5rem;
  }
  
  .rating-value {
    font-size: 1.25rem;
  }
}

@media screen and (max-width: 480px) {
  .product-reviews {
    padding: 1rem;
    border-radius: 12px;
  }

  .reviews-title {
    font-size: 1.25rem;
  }

  .reviews-summary {
    padding: 0.75rem;
  }

  .review-form-container {
    padding: 1.5rem;
  }

  .star-rating-input .star {
    font-size: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn {
    width: 100%;
    padding: 1rem;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Check if Judge.me is loaded
  function checkJudgeMeLoaded() {
    if (typeof window.jdgm !== 'undefined' && window.jdgm.widgets) {
      // Judge.me is loaded, hide fallback
      const fallback = document.querySelector('.reviews-fallback');
      if (fallback) {
        fallback.style.display = 'none';
      }
    } else {
      // Judge.me not loaded, show fallback after a delay
      setTimeout(() => {
        if (typeof window.jdgm === 'undefined' || !window.jdgm.widgets) {
          const fallback = document.querySelector('.reviews-fallback');
          if (fallback) {
            fallback.style.display = 'block';
          }
        }
      }, 3000);
    }
  }

  // Check immediately and after a delay
  checkJudgeMeLoaded();
  setTimeout(checkJudgeMeLoaded, 1000);
});

// Review form functions
function toggleReviewForm() {
  const form = document.getElementById('reviewForm');
  const success = document.getElementById('reviewSuccess');

  if (form.style.display === 'none' || form.style.display === '') {
    form.style.display = 'block';
    success.style.display = 'none';
    form.scrollIntoView({ behavior: 'smooth', block: 'center' });
  } else {
    form.style.display = 'none';
  }
}

function submitReview(event) {
  event.preventDefault();

  const form = event.target;
  const formData = new FormData(form);

  // Get form values
  const rating = formData.get('rating');
  const review = formData.get('review');
  const name = formData.get('name');
  const email = formData.get('email');
  const productId = formData.get('product_id');
  const productTitle = formData.get('product_title');

  // Validate rating
  if (!rating) {
    alert('{{ "products.reviews.please_select_rating" | t | default: "Please select a rating" }}');
    return;
  }

  // Show loading state
  const submitBtn = form.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  submitBtn.textContent = '{{ "products.reviews.submitting" | t | default: "Submitting..." }}';
  submitBtn.disabled = true;

  // Prepare review data for contact form submission
  const reviewData = {
    subject: `Product Review - ${productTitle}`,
    message: `
New Product Review:

Product: ${productTitle}
Rating: ${rating}/5 stars
Reviewer: ${name}
Email: ${email}

Review:
${review}

---
This review was submitted through the product page review form.
    `.trim(),
    contact: {
      name: name,
      email: email
    }
  };

  // Try to submit through contact form or Judge.me
  submitReviewData(reviewData).then(() => {
    // Show success message
    document.getElementById('reviewForm').style.display = 'none';
    document.getElementById('reviewSuccess').style.display = 'block';
    document.getElementById('reviewSuccess').scrollIntoView({ behavior: 'smooth' });

    // Reset form
    form.reset();

    // Reset submit button
    submitBtn.textContent = originalText;
    submitBtn.disabled = false;

  }).catch((error) => {
    console.error('Review submission error:', error);
    alert('{{ "products.reviews.submission_error" | t | default: "There was an error submitting your review. Please try again." }}');

    // Reset submit button
    submitBtn.textContent = originalText;
    submitBtn.disabled = false;
  });
}

async function submitReviewData(reviewData) {
  // Try Judge.me first if available
  if (typeof window.jdgm !== 'undefined' && window.jdgm.widgets) {
    try {
      // Judge.me submission logic would go here
      console.log('Submitting to Judge.me:', reviewData);
    } catch (error) {
      console.log('Judge.me submission failed, using fallback');
    }
  }

  // Fallback: Try to submit through contact form
  const contactForm = document.querySelector('#contact-form form, .contact-form form');
  if (contactForm) {
    try {
      // Fill contact form fields
      const nameField = contactForm.querySelector('input[name*="name"], input[name*="contact[name]"]');
      const emailField = contactForm.querySelector('input[name*="email"], input[name*="contact[email]"]');
      const messageField = contactForm.querySelector('textarea[name*="message"], textarea[name*="body"], textarea[name*="contact[body]"]');

      if (nameField) nameField.value = reviewData.contact.name;
      if (emailField) emailField.value = reviewData.contact.email;
      if (messageField) messageField.value = reviewData.message;

      // Submit the contact form
      const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
      contactForm.dispatchEvent(submitEvent);

      return Promise.resolve();
    } catch (error) {
      console.error('Contact form submission failed:', error);
    }
  }

  // Final fallback: Store in localStorage and show success
  try {
    const reviews = JSON.parse(localStorage.getItem('pendingReviews') || '[]');
    reviews.push({
      ...reviewData,
      timestamp: new Date().toISOString(),
      productId: reviewData.productId
    });
    localStorage.setItem('pendingReviews', JSON.stringify(reviews));

    return Promise.resolve();
  } catch (error) {
    return Promise.reject(error);
  }
}

// Fallback review form function (for backward compatibility)
function openReviewForm() {
  toggleReviewForm();
}
</script>
