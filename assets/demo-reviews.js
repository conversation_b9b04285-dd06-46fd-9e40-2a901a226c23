// Demo Reviews Data for Testing
// This script adds sample reviews to localStorage for demonstration purposes

document.addEventListener('DOMContentLoaded', function() {
  // Check if demo reviews already exist
  const existingReviews = localStorage.getItem('productReviews');
  if (existingReviews && JSON.parse(existingReviews).demo) {
    return; // Demo reviews already added
  }

  // Sample reviews data
  const demoReviews = {
    // You can add product IDs and their reviews here
    'demo': [
      {
        rating: 5,
        review: 'Amazing tour! The guide was knowledgeable and the scenery was breathtaking. Highly recommend this experience to anyone visiting Georgia.',
        name: '<PERSON>',
        email: '<EMAIL>',
        productId: 'demo',
        productTitle: 'Demo Tour',
        timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
        approved: true
      },
      {
        rating: 4,
        review: 'Great tour with beautiful views. The only downside was that it was a bit rushed, but overall a wonderful experience.',
        name: '<PERSON>',
        email: 'mi<PERSON><PERSON>@example.com',
        productId: 'demo',
        productTitle: 'Demo Tour',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
        approved: true
      },
      {
        rating: 5,
        review: 'Absolutely fantastic! The tour exceeded all my expectations. The local cuisine was delicious and the cultural insights were invaluable.',
        name: 'Emma Rodriguez',
        email: '<EMAIL>',
        productId: 'demo',
        productTitle: 'Demo Tour',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
        approved: true
      }
    ]
  };

  // Add demo reviews to localStorage
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  Object.assign(allReviews, demoReviews);
  localStorage.setItem('productReviews', JSON.stringify(allReviews));

  console.log('Demo reviews added to localStorage');
});

// Function to add reviews for specific product (for testing)
function addDemoReviewsForProduct(productId, productTitle) {
  const demoReviews = [
    {
      rating: 5,
      review: 'Incredible experience! This tour was the highlight of our trip to Georgia. The guide was professional and the locations were stunning.',
      name: 'David Wilson',
      email: '<EMAIL>',
      productId: productId,
      productTitle: productTitle,
      timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      approved: true
    },
    {
      rating: 4,
      review: 'Very good tour with excellent organization. The food was amazing and we learned so much about Georgian culture.',
      name: 'Lisa Thompson',
      email: '<EMAIL>',
      productId: productId,
      productTitle: productTitle,
      timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      approved: true
    },
    {
      rating: 5,
      review: 'Perfect tour! Everything was well-planned and our guide was fantastic. Would definitely book again.',
      name: 'James Miller',
      email: '<EMAIL>',
      productId: productId,
      productTitle: productTitle,
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      approved: true
    }
  ];

  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  if (!allReviews[productId]) {
    allReviews[productId] = [];
  }
  allReviews[productId] = [...allReviews[productId], ...demoReviews];
  localStorage.setItem('productReviews', JSON.stringify(allReviews));

  // Trigger reviews reload if on product page
  if (typeof loadReviews === 'function') {
    loadReviews(productId);
  }
  if (typeof updateRatingSummary === 'function') {
    updateRatingSummary();
  }

  console.log(`Demo reviews added for product ${productId}`);
}

// Function to clear all reviews (for testing)
function clearAllReviews() {
  localStorage.removeItem('productReviews');
  console.log('All reviews cleared');
  
  // Reload page to reflect changes
  if (confirm('All reviews have been cleared. Reload page to see changes?')) {
    window.location.reload();
  }
}

// Make functions available globally for testing
window.addDemoReviewsForProduct = addDemoReviewsForProduct;
window.clearAllReviews = clearAllReviews;
