<style>
.reviews-admin {
  padding: 2rem 0;
  background: #f8fafc;
  min-height: 100vh;
}

.reviews-admin__header {
  text-align: center;
  margin-bottom: 3rem;
}

.reviews-admin__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.reviews-admin__description {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

.reviews-stats {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.reviews-controls {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.controls-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.search-row {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.search-input,
.filter-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  flex: 1;
  min-width: 200px;
}

.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn--secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn--secondary:hover {
  background: #e5e7eb;
}

.btn--danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.btn--danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.btn--small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.reviews-list-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.admin-reviews-list {
  display: flex;
  flex-direction: column;
}

.admin-review-item {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.admin-review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.review-info {
  flex: 1;
}

.product-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 0.5rem;
}

.review-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.875rem;
  color: #64748b;
}

.reviewer-name {
  font-weight: 600;
  color: #374151;
}

.review-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.rating-display .rating {
  margin: 0;
}

.review-content p {
  color: #374151;
  line-height: 1.6;
  margin: 0;
}

.no-reviews {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.no-reviews h3 {
  margin-bottom: 0.5rem;
  color: #374151;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .reviews-admin__title {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .controls-row {
    flex-direction: column;
  }

  .search-row {
    flex-direction: column;
  }

  .search-input,
  .filter-select {
    min-width: auto;
  }

  .review-header {
    flex-direction: column;
    gap: 1rem;
  }

  .review-meta {
    flex-direction: column;
    gap: 0.25rem;
  }

  .review-actions {
    align-self: flex-start;
  }
}
</style>

<div class="reviews-admin">
  <div class="page-width">
    <div class="reviews-admin__header">
      <h1 class="reviews-admin__title">{{ section.settings.heading }}</h1>
      {%- if section.settings.description != blank -%}
        <p class="reviews-admin__description">{{ section.settings.description }}</p>
      {%- endif -%}
    </div>

    <div class="reviews-admin__content">
      <!-- Statistics -->
      <div class="reviews-stats">
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number" id="totalReviews">0</div>
            <div class="stat-label">Total Reviews</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="averageRating">0.0</div>
            <div class="stat-label">Average Rating</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="totalProducts">0</div>
            <div class="stat-label">Products with Reviews</div>
          </div>
        </div>
      </div>

      <!-- Controls -->
      <div class="reviews-controls">
        <div class="controls-row">
          <button class="btn btn--primary" onclick="exportReviews()">Export Reviews</button>
          <button class="btn btn--secondary" onclick="importDemoReviews()">Add Demo Reviews</button>
          <button class="btn btn--danger" onclick="clearAllReviews()">Clear All Reviews</button>
        </div>
        <div class="search-row">
          <input type="text" id="searchReviews" placeholder="Search reviews..." class="search-input">
          <select id="filterRating" class="filter-select">
            <option value="">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="2">2 Stars</option>
            <option value="1">1 Star</option>
          </select>
        </div>
      </div>

      <!-- Reviews List -->
      <div class="reviews-list-container">
        <div id="reviewsList" class="admin-reviews-list">
          <!-- Reviews will be loaded here -->
        </div>
        <div id="noReviews" class="no-reviews" style="display: none;">
          <h3>No reviews found</h3>
          <p>There are no reviews to display.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  loadAdminReviews();
  updateStats();
  
  // Search functionality
  document.getElementById('searchReviews').addEventListener('input', filterReviews);
  document.getElementById('filterRating').addEventListener('change', filterReviews);
});

function loadAdminReviews() {
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  const reviewsList = document.getElementById('reviewsList');
  const noReviews = document.getElementById('noReviews');
  
  let allReviewsArray = [];
  
  // Convert to flat array
  Object.keys(allReviews).forEach(productId => {
    allReviews[productId].forEach(review => {
      allReviewsArray.push({...review, productId});
    });
  });
  
  if (allReviewsArray.length === 0) {
    reviewsList.innerHTML = '';
    noReviews.style.display = 'block';
    return;
  }
  
  noReviews.style.display = 'none';
  
  // Sort by date (newest first)
  allReviewsArray.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  
  reviewsList.innerHTML = allReviewsArray.map((review, index) => `
    <div class="admin-review-item" data-rating="${review.rating}" data-product="${review.productId}">
      <div class="review-header">
        <div class="review-info">
          <h4 class="product-title">${escapeHtml(review.productTitle)}</h4>
          <div class="review-meta">
            <span class="reviewer-name">${escapeHtml(review.name)}</span>
            <span class="review-email">${escapeHtml(review.email)}</span>
            <span class="review-date">${formatDate(review.timestamp)}</span>
          </div>
        </div>
        <div class="review-actions">
          <div class="rating-display">
            <div class="rating">
              <span class="rating-star" style="--rating: ${review.rating}; --rating-max: 5;"></span>
            </div>
          </div>
          <button class="btn btn--small btn--danger" onclick="deleteReview('${review.productId}', ${index})">Delete</button>
        </div>
      </div>
      <div class="review-content">
        <p>${escapeHtml(review.review).replace(/\n/g, '<br>')}</p>
      </div>
    </div>
  `).join('');
}

function updateStats() {
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  
  let totalReviews = 0;
  let totalRating = 0;
  let productsWithReviews = 0;
  
  Object.keys(allReviews).forEach(productId => {
    if (allReviews[productId].length > 0) {
      productsWithReviews++;
      totalReviews += allReviews[productId].length;
      allReviews[productId].forEach(review => {
        totalRating += parseInt(review.rating);
      });
    }
  });
  
  const averageRating = totalReviews > 0 ? (totalRating / totalReviews).toFixed(1) : 0;
  
  document.getElementById('totalReviews').textContent = totalReviews;
  document.getElementById('averageRating').textContent = averageRating;
  document.getElementById('totalProducts').textContent = productsWithReviews;
}

function filterReviews() {
  const searchTerm = document.getElementById('searchReviews').value.toLowerCase();
  const ratingFilter = document.getElementById('filterRating').value;
  const reviewItems = document.querySelectorAll('.admin-review-item');
  
  reviewItems.forEach(item => {
    const text = item.textContent.toLowerCase();
    const rating = item.dataset.rating;
    
    const matchesSearch = searchTerm === '' || text.includes(searchTerm);
    const matchesRating = ratingFilter === '' || rating === ratingFilter;
    
    item.style.display = matchesSearch && matchesRating ? 'block' : 'none';
  });
}

function deleteReview(productId, reviewIndex) {
  if (!confirm('Are you sure you want to delete this review?')) {
    return;
  }
  
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  
  if (allReviews[productId] && allReviews[productId][reviewIndex]) {
    allReviews[productId].splice(reviewIndex, 1);
    
    // Remove product if no reviews left
    if (allReviews[productId].length === 0) {
      delete allReviews[productId];
    }
    
    localStorage.setItem('productReviews', JSON.stringify(allReviews));
    
    // Reload
    loadAdminReviews();
    updateStats();
  }
}

function exportReviews() {
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  const dataStr = JSON.stringify(allReviews, null, 2);
  const dataBlob = new Blob([dataStr], {type: 'application/json'});
  
  const link = document.createElement('a');
  link.href = URL.createObjectURL(dataBlob);
  link.download = 'reviews-export-' + new Date().toISOString().split('T')[0] + '.json';
  link.click();
}

function importDemoReviews() {
  if (confirm('This will add demo reviews for testing. Continue?')) {
    // Add demo reviews for current products
    const productIds = ['demo', 'test-product-1', 'test-product-2'];
    
    productIds.forEach(productId => {
      if (typeof addDemoReviewsForProduct === 'function') {
        addDemoReviewsForProduct(productId, `Demo Product ${productId}`);
      }
    });
    
    setTimeout(() => {
      loadAdminReviews();
      updateStats();
    }, 100);
  }
}

function clearAllReviews() {
  if (confirm('Are you sure you want to delete ALL reviews? This cannot be undone.')) {
    localStorage.removeItem('productReviews');
    loadAdminReviews();
    updateStats();
  }
}

// Helper functions
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function formatDate(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}
</script>

{% schema %}
{
  "name": "Reviews Admin",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Reviews Management",
      "label": "Heading"
    },
    {
      "type": "textarea",
      "id": "description",
      "default": "Manage customer reviews for all products",
      "label": "Description"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "Reviews Admin"
    }
  ]
}
{% endschema %}
