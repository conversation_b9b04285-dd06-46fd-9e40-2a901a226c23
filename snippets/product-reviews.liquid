{% comment %}
  Renders a comprehensive reviews section for tour products

  Accepts:
  - product: {Object} Product object
  - block: {Object} Block object (optional)

  Usage:
  {% render 'product-reviews', product: product %}
  {% render 'product-reviews', product: product, block: block %}
{% endcomment %}

{%- liquid
  assign show_rating_summary = true
  assign show_write_review_button = true
  assign reviews_heading = 'Customer Reviews'

  if block
    assign show_rating_summary = block.settings.show_rating_summary
    assign show_write_review_button = block.settings.show_write_review_button
  endif

  if section
    assign show_rating_summary = section.settings.show_rating_summary
    assign show_write_review_button = section.settings.show_write_review_button
    assign reviews_heading = section.settings.heading
  endif
-%}

<div class="product-reviews">
  <div class="product-reviews__container">
    <!-- Reviews Header -->
    <div class="reviews-header">
      <h3 class="reviews-title">{{ reviews_heading }}</h3>
      
      <!-- Rating Summary -->
      {%- if show_rating_summary -%}
        <div class="reviews-summary" id="reviewsSummary" style="display: none;">
          <div class="rating-display">
            <div class="rating rating--large" role="img" id="averageRatingDisplay">
              <span aria-hidden="true" class="rating-star" id="ratingStars"></span>
            </div>
            <div class="rating-info">
              <span class="rating-value" id="averageRating">0</span>
              <span class="rating-max">/ 5</span>
              <span class="rating-count" id="reviewCount">(0 {{ 'products.reviews.reviews' | t | default: 'reviews' }})</span>
            </div>
          </div>
        </div>
      {%- endif -%}
    </div>

    <!-- Custom Reviews Display -->
    <div class="reviews-display">
      <div id="reviewsList" class="reviews-list">
        <!-- Reviews will be loaded here -->
      </div>

      <!-- No Reviews Placeholder -->
      <div class="no-reviews-placeholder" id="noReviewsPlaceholder">
        <div class="reviews-placeholder">
          <h4>{{ 'products.reviews.no_reviews_yet' | t | default: 'No reviews yet' }}</h4>
          <p>{{ 'products.reviews.be_first_to_review' | t | default: 'Be the first to write a review' }}</p>
        </div>
      </div>
    </div>

    <!-- Custom Review Form (Always Available) -->
    <div class="custom-review-form">
      <div class="review-form-toggle">
        <button class="btn btn--primary" onclick="toggleReviewForm()">
          {{ 'products.reviews.write_review' | t | default: 'Write a Review' }}
        </button>
      </div>

      <div class="review-form-container" id="reviewForm" style="display: none;">
        <form class="review-form" onsubmit="submitReview(event)">
          <h4>{{ 'products.reviews.write_your_review' | t | default: 'Write Your Review' }}</h4>

          <!-- Star Rating Input -->
          <div class="rating-input">
            <label>{{ 'products.reviews.your_rating' | t | default: 'Your Rating' }}:</label>
            <div class="star-rating-input">
              <input type="radio" name="rating" value="5" id="star5">
              <label for="star5" class="star">★</label>
              <input type="radio" name="rating" value="4" id="star4">
              <label for="star4" class="star">★</label>
              <input type="radio" name="rating" value="3" id="star3">
              <label for="star3" class="star">★</label>
              <input type="radio" name="rating" value="2" id="star2">
              <label for="star2" class="star">★</label>
              <input type="radio" name="rating" value="1" id="star1">
              <label for="star1" class="star">★</label>
            </div>
          </div>

          <!-- Review Text -->
          <div class="form-group">
            <label for="reviewText">{{ 'products.reviews.your_review' | t | default: 'Your Review' }}:</label>
            <textarea id="reviewText" name="review" rows="4" required
                      placeholder="{{ 'products.reviews.review_placeholder' | t | default: 'Share your experience with this tour...' }}"></textarea>
          </div>

          <!-- Reviewer Name -->
          <div class="form-group">
            <label for="reviewerName">{{ 'products.reviews.your_name' | t | default: 'Your Name' }}:</label>
            <input type="text" id="reviewerName" name="name" required
                   placeholder="{{ 'products.reviews.name_placeholder' | t | default: 'Enter your name' }}">
          </div>

          <!-- Email -->
          <div class="form-group">
            <label for="reviewerEmail">{{ 'products.reviews.your_email' | t | default: 'Your Email' }}:</label>
            <input type="email" id="reviewerEmail" name="email" required
                   placeholder="{{ 'products.reviews.email_placeholder' | t | default: 'Enter your email' }}">
          </div>

          <input type="hidden" name="product_id" value="{{ product.id }}">
          <input type="hidden" name="product_title" value="{{ product.title }}">

          <div class="form-actions">
            <button type="submit" class="btn btn--primary">
              {{ 'products.reviews.submit_review' | t | default: 'Submit Review' }}
            </button>
            <button type="button" class="btn btn--secondary" onclick="toggleReviewForm()">
              {{ 'products.reviews.cancel' | t | default: 'Cancel' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Review Success Message -->
    <div class="review-success" id="reviewSuccess" style="display: none;">
      <div class="success-message">
        <h4>{{ 'products.reviews.thank_you' | t | default: 'Thank You!' }}</h4>
        <p>{{ 'products.reviews.review_submitted' | t | default: 'Your review has been submitted and will be published after moderation.' }}</p>
      </div>
    </div>
  </div>
</div>

<style>
.product-reviews {
  background: #ffffff;
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reviews-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-align: center;
}

.reviews-summary {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.rating--large .rating-star {
  font-size: 2rem;
  --letter-spacing: 0.2;
}

.rating-info {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  font-size: 1.1rem;
}

.rating-value {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.5rem;
}

.rating-max {
  color: #64748b;
  font-size: 1.2rem;
}

.rating-count {
  color: #64748b;
  font-size: 1rem;
  margin-left: 0.5rem;
}

/* Reviews Display */
.reviews-display {
  margin-top: 2rem;
}

.reviews-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.review-item {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
}

.review-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.review-rating .rating {
  margin-bottom: 0;
}

.review-meta {
  text-align: right;
}

.reviewer-name {
  font-weight: 600;
  color: #1e293b;
  display: block;
  margin-bottom: 0.25rem;
}

.review-date {
  font-size: 0.875rem;
  color: #64748b;
}

.review-content p {
  color: #374151;
  line-height: 1.6;
  margin: 0;
}

.no-reviews-placeholder {
  text-align: center;
  padding: 3rem 2rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #cbd5e1;
}

.reviews-placeholder h4 {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.reviews-placeholder p {
  color: #94a3b8;
  margin-bottom: 1.5rem;
}

/* Custom Review Form Styling */
.custom-review-form {
  margin-top: 2rem;
}

.review-form-toggle {
  text-align: center;
  margin-bottom: 1rem;
}

.review-form-container {
  background: #f8fafc;
  border-radius: 12px;
  padding: 2rem;
  margin-top: 1rem;
  border: 1px solid rgba(148, 163, 184, 0.2);
}

.review-form h4 {
  color: #1e293b;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Star Rating Input */
.rating-input {
  margin-bottom: 1.5rem;
}

.rating-input label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #374151;
}

.star-rating-input {
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
  gap: 0.25rem;
}

.star-rating-input input[type="radio"] {
  display: none;
}

.star-rating-input .star {
  font-size: 2rem;
  color: #d1d5db;
  cursor: pointer;
  transition: color 0.2s ease;
  width: auto;
  padding: 0;
  border: none;
  background: none;
}

.star-rating-input .star:hover,
.star-rating-input .star:hover ~ .star,
.star-rating-input input[type="radio"]:checked ~ .star {
  color: #fbbf24;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.btn--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn--secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn--secondary:hover {
  background: #e5e7eb;
}

/* Success Message */
.review-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  margin-top: 1rem;
}

.success-message h4 {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.success-message p {
  margin: 0;
  opacity: 0.9;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .product-reviews {
    padding: 1.5rem;
    margin-top: 1.5rem;
  }

  .reviews-title {
    font-size: 1.5rem;
  }

  .rating-display {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .rating--large .rating-star {
    font-size: 1.5rem;
  }

  .rating-value {
    font-size: 1.25rem;
  }

  .review-header {
    flex-direction: column;
    gap: 0.75rem;
  }

  .review-meta {
    text-align: left;
  }

  .review-item {
    padding: 1rem;
  }

  .no-reviews-placeholder {
    padding: 2rem 1rem;
  }
}

@media screen and (max-width: 480px) {
  .product-reviews {
    padding: 1rem;
    border-radius: 12px;
  }

  .reviews-title {
    font-size: 1.25rem;
  }

  .reviews-summary {
    padding: 0.75rem;
  }

  .review-form-container {
    padding: 1.5rem;
  }

  .star-rating-input .star {
    font-size: 1.5rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .btn {
    width: 100%;
    padding: 1rem;
  }
}
</style>

<script>
// Custom Reviews System
document.addEventListener('DOMContentLoaded', function() {
  const productId = window.testProductId || '{{ product.id }}' || 'test-product';

  // Load existing reviews
  loadReviews(productId);

  // Update rating summary
  updateRatingSummary(productId);
});

// Load reviews from localStorage
function loadReviews(productId) {
  const reviews = getProductReviews(productId);
  const reviewsList = document.getElementById('reviewsList');
  const noReviewsPlaceholder = document.getElementById('noReviewsPlaceholder');

  if (reviews.length === 0) {
    reviewsList.innerHTML = '';
    noReviewsPlaceholder.style.display = 'block';
    return;
  }

  noReviewsPlaceholder.style.display = 'none';

  // Sort reviews by date (newest first)
  reviews.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

  reviewsList.innerHTML = reviews.map(review => `
    <div class="review-item">
      <div class="review-header">
        <div class="review-rating">
          <div class="rating">
            <span class="rating-star" style="--rating: ${review.rating}; --rating-max: 5;"></span>
          </div>
        </div>
        <div class="review-meta">
          <span class="reviewer-name">${escapeHtml(review.name)}</span>
          <span class="review-date">${formatDate(review.timestamp)}</span>
        </div>
      </div>
      <div class="review-content">
        <p>${escapeHtml(review.review).replace(/\n/g, '<br>')}</p>
      </div>
    </div>
  `).join('');
}

// Get reviews for specific product
function getProductReviews(productId) {
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  return allReviews[productId] || [];
}

// Update rating summary
function updateRatingSummary(productId) {
  productId = productId || window.testProductId || '{{ product.id }}' || 'test-product';
  const reviews = getProductReviews(productId);

  if (reviews.length === 0) {
    document.getElementById('reviewsSummary').style.display = 'none';
    return;
  }

  const totalRating = reviews.reduce((sum, review) => sum + parseInt(review.rating), 0);
  const averageRating = (totalRating / reviews.length).toFixed(1);

  document.getElementById('reviewsSummary').style.display = 'flex';
  document.getElementById('averageRating').textContent = averageRating;
  document.getElementById('reviewCount').textContent = `(${reviews.length} ${reviews.length === 1 ? 'review' : 'reviews'})`;
  document.getElementById('ratingStars').style.setProperty('--rating', Math.floor(averageRating));
  document.getElementById('ratingStars').style.setProperty('--rating-max', '5');

  // Set decimal rating
  const decimal = averageRating % 1;
  let ratingDecimal = 0;
  if (decimal >= 0.3 && decimal <= 0.7) {
    ratingDecimal = 0.5;
  } else if (decimal > 0.7) {
    ratingDecimal = 1;
  }
  document.getElementById('ratingStars').style.setProperty('--rating-decimal', ratingDecimal);
}

// Helper functions
function escapeHtml(text) {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

function formatDate(timestamp) {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}</script>

// Review form functions
function toggleReviewForm() {
  const form = document.getElementById('reviewForm');
  const success = document.getElementById('reviewSuccess');

  if (form.style.display === 'none' || form.style.display === '') {
    form.style.display = 'block';
    success.style.display = 'none';
    form.scrollIntoView({ behavior: 'smooth', block: 'center' });
  } else {
    form.style.display = 'none';
  }
}

function submitReview(event) {
  event.preventDefault();

  const form = event.target;
  const formData = new FormData(form);

  // Get form values
  const rating = formData.get('rating');
  const review = formData.get('review');
  const name = formData.get('name');
  const email = formData.get('email');
  const productId = formData.get('product_id');
  const productTitle = formData.get('product_title');

  // Validate rating
  if (!rating) {
    alert('{{ "products.reviews.please_select_rating" | t | default: "Please select a rating" }}');
    return;
  }

  // Show loading state
  const submitBtn = form.querySelector('button[type="submit"]');
  const originalText = submitBtn.textContent;
  submitBtn.textContent = '{{ "products.reviews.submitting" | t | default: "Submitting..." }}';
  submitBtn.disabled = true;

  // Create review object
  const reviewData = {
    rating: parseInt(rating),
    review: review.trim(),
    name: name.trim(),
    email: email.trim(),
    productId: productId,
    productTitle: productTitle,
    timestamp: new Date().toISOString(),
    approved: true // Auto-approve for now
  };

  try {
    // Save review to localStorage
    saveReview(reviewData);

    // Show success message
    document.getElementById('reviewForm').style.display = 'none';
    document.getElementById('reviewSuccess').style.display = 'block';
    document.getElementById('reviewSuccess').scrollIntoView({ behavior: 'smooth' });

    // Reset form
    form.reset();

    // Reload reviews display
    loadReviews(productId);
    updateRatingSummary(productId);

    // Reset submit button
    submitBtn.textContent = originalText;
    submitBtn.disabled = false;

  } catch (error) {
    console.error('Review submission error:', error);
    alert('{{ "products.reviews.submission_error" | t | default: "There was an error submitting your review. Please try again." }}');

    // Reset submit button
    submitBtn.textContent = originalText;
    submitBtn.disabled = false;
  }
}

// Save review to localStorage
function saveReview(reviewData) {
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');

  if (!allReviews[reviewData.productId]) {
    allReviews[reviewData.productId] = [];
  }

  allReviews[reviewData.productId].push(reviewData);
  localStorage.setItem('productReviews', JSON.stringify(allReviews));
}



// Fallback review form function (for backward compatibility)
function openReviewForm() {
  toggleReviewForm();
}
</script>
