{{ 'component-rating.css' | asset_url | stylesheet_tag }}

<section class="product-reviews-section section-{{ section.id }}-padding gradient color-{{ section.settings.color_scheme }}">
  <div class="page-width">
    {% render 'product-reviews', product: product, section: section %}
  </div>
</section>

{% schema %}
{
  "name": "Product Reviews",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Reviews Settings"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "Customer Reviews",
      "label": "Section heading"
    },
    {
      "type": "checkbox",
      "id": "show_rating_summary",
      "default": true,
      "label": "Show rating summary"
    },
    {
      "type": "checkbox",
      "id": "show_write_review_button",
      "default": true,
      "label": "Show write review button"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        },
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "templates": ["product"],
  "presets": [
    {
      "name": "Product Reviews"
    }
  ]
}
{% endschema %}
