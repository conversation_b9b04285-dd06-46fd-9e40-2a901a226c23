<div class="test-reviews">
  <div class="page-width">
    <div class="test-reviews__header">
      <h1 class="test-reviews__title">{{ section.settings.heading }}</h1>
      {%- if section.settings.description != blank -%}
        <p class="test-reviews__description">{{ section.settings.description }}</p>
      {%- endif -%}
    </div>

    <div class="test-reviews__content">
      <!-- Test Controls -->
      <div class="test-controls">
        <h3>Test Controls</h3>
        <div class="controls-grid">
          <button class="btn btn--primary" onclick="addTestReviews()">Add Test Reviews</button>
          <button class="btn btn--secondary" onclick="clearTestReviews()">Clear Reviews</button>
          <button class="btn btn--secondary" onclick="showReviewsData()">Show Data</button>
        </div>
      </div>

      <!-- Mock Product for Testing -->
      <div class="mock-product">
        <h2>Test Product: 5-Day Adventure Tour</h2>
        <p>This is a mock product to test the reviews system.</p>
        
        <!-- Product Reviews Section -->
        {% assign mock_product = collections.all.products.first %}
        {% unless mock_product %}
          {% assign mock_product = shop %}
        {% endunless %}
        
        <div class="mock-product-reviews">
          {% render 'product-reviews', product: mock_product %}
        </div>
      </div>

      <!-- Data Display -->
      <div class="data-display" id="dataDisplay" style="display: none;">
        <h3>Reviews Data</h3>
        <pre id="reviewsData"></pre>
      </div>
    </div>
  </div>
</div>

<style>
.test-reviews {
  padding: 2rem 0;
  background: #f8fafc;
  min-height: 100vh;
}

.test-reviews__header {
  text-align: center;
  margin-bottom: 3rem;
}

.test-reviews__title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.test-reviews__description {
  font-size: 1.1rem;
  color: #64748b;
  max-width: 600px;
  margin: 0 auto;
}

.test-controls {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-controls h3 {
  margin-bottom: 1rem;
  color: #1e293b;
}

.controls-grid {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.mock-product {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mock-product h2 {
  color: #1e293b;
  margin-bottom: 1rem;
}

.mock-product p {
  color: #64748b;
  margin-bottom: 2rem;
}

.data-display {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-display h3 {
  margin-bottom: 1rem;
  color: #1e293b;
}

.data-display pre {
  background: #f1f5f9;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 0.875rem;
  color: #374151;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn--primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.btn--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn--secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn--secondary:hover {
  background: #e5e7eb;
}

@media screen and (max-width: 768px) {
  .test-reviews__title {
    font-size: 2rem;
  }
  
  .controls-grid {
    flex-direction: column;
  }
  
  .test-controls,
  .mock-product,
  .data-display {
    padding: 1.5rem;
  }
}
</style>

<script>
function addTestReviews() {
  const testReviews = [
    {
      rating: 5,
      review: 'Amazing tour! The guide was fantastic and the scenery was breathtaking. Highly recommend this experience to anyone visiting Georgia.',
      name: 'John Smith',
      email: '<EMAIL>',
      productId: 'test-product',
      productTitle: '5-Day Adventure Tour',
      timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      approved: true
    },
    {
      rating: 4,
      review: 'Great tour with beautiful views. The accommodation was comfortable and the food was delicious. Only minor issue was the weather, but that\'s not controllable!',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      productId: 'test-product',
      productTitle: '5-Day Adventure Tour',
      timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      approved: true
    },
    {
      rating: 5,
      review: 'Absolutely perfect! This tour exceeded all my expectations. The cultural experiences were authentic and the natural beauty was stunning.',
      name: 'Michael Chen',
      email: '<EMAIL>',
      productId: 'test-product',
      productTitle: '5-Day Adventure Tour',
      timestamp: new Date().toISOString(),
      approved: true
    }
  ];

  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  allReviews['test-product'] = testReviews;
  localStorage.setItem('productReviews', JSON.stringify(allReviews));

  // Reload reviews if functions are available
  if (typeof loadReviews === 'function') {
    loadReviews('test-product');
  }
  if (typeof updateRatingSummary === 'function') {
    updateRatingSummary();
  }

  alert('Test reviews added! Check the reviews section below.');
}

function clearTestReviews() {
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  delete allReviews['test-product'];
  localStorage.setItem('productReviews', JSON.stringify(allReviews));

  // Reload reviews if functions are available
  if (typeof loadReviews === 'function') {
    loadReviews('test-product');
  }
  if (typeof updateRatingSummary === 'function') {
    updateRatingSummary();
  }

  alert('Test reviews cleared!');
}

function showReviewsData() {
  const allReviews = JSON.parse(localStorage.getItem('productReviews') || '{}');
  const dataDisplay = document.getElementById('dataDisplay');
  const reviewsData = document.getElementById('reviewsData');
  
  reviewsData.textContent = JSON.stringify(allReviews, null, 2);
  dataDisplay.style.display = dataDisplay.style.display === 'none' ? 'block' : 'none';
}

// Override product ID for testing
document.addEventListener('DOMContentLoaded', function() {
  // Replace the product ID in the reviews script
  const scripts = document.querySelectorAll('script');
  scripts.forEach(script => {
    if (script.textContent && script.textContent.includes('const productId =')) {
      // This is a bit hacky, but for testing purposes
      window.testProductId = 'test-product';
    }
  });
});
</script>

{% schema %}
{
  "name": "Test Reviews",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Test Reviews System",
      "label": "Heading"
    },
    {
      "type": "textarea",
      "id": "description",
      "default": "Test our custom reviews system without Judge.me",
      "label": "Description"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "Test Reviews"
    }
  ]
}
{% endschema %}
