{% comment %}
  Renders a comprehensive reviews section for tour products

  Accepts:
  - product: {Object} Product object
  - block: {Object} Block object (optional)

  Usage:
  {% render 'product-reviews', product: product %}
  {% render 'product-reviews', product: product, block: block %}
{% endcomment %}

{%- liquid
  assign show_rating_summary = true
  assign show_write_review_button = true
  assign reviews_heading = 'Customer Reviews'

  if block
    assign show_rating_summary = block.settings.show_rating_summary
    assign show_write_review_button = block.settings.show_write_review_button
  endif

  if section
    assign show_rating_summary = section.settings.show_rating_summary
    assign show_write_review_button = section.settings.show_write_review_button
    assign reviews_heading = section.settings.heading
  endif
-%}

<div class="product-reviews">
  <div class="product-reviews__container">
    <!-- Reviews Header -->
    <div class="reviews-header">
      <h3 class="reviews-title">{{ reviews_heading }}</h3>
      
      <!-- Rating Summary -->
      {%- if show_rating_summary and product.metafields.reviews.rating.value != blank -%}
        <div class="reviews-summary">
          {% liquid
            assign rating_decimal = 0
            assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
            if decimal >= 0.3 and decimal <= 0.7
              assign rating_decimal = 0.5
            elsif decimal > 0.7
              assign rating_decimal = 1
            endif
          %}
          <div class="rating-display">
            <div
              class="rating rating--large"
              role="img"
              aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
            >
              <span
                aria-hidden="true"
                class="rating-star"
                style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
              ></span>
            </div>
            <div class="rating-info">
              <span class="rating-value">{{ product.metafields.reviews.rating.value }}</span>
              <span class="rating-max">/ {{ product.metafields.reviews.rating.value.scale_max }}</span>
              <span class="rating-count">({{ product.metafields.reviews.rating_count }} {{ 'products.reviews.reviews' | t | default: 'reviews' }})</span>
            </div>
          </div>
        </div>
      {%- endif -%}
    </div>

    <!-- Judge.me Reviews Widget -->
    <div class="reviews-widget">
      <!-- Judge.me Reviews Tab Widget -->
      <div class="jdgm-widget jdgm-review-widget" data-id="{{ product.id }}"></div>
      
      <!-- Judge.me Write Review Button -->
      {%- if show_write_review_button -%}
        <div class="reviews-actions">
          <div class="jdgm-widget jdgm-write-review-button" data-id="{{ product.id }}"></div>
        </div>
      {%- endif -%}
    </div>

    <!-- Fallback for when Judge.me is not loaded -->
    <div class="reviews-fallback" style="display: none;">
      <div class="reviews-placeholder">
        <h4>{{ 'products.reviews.no_reviews_yet' | t | default: 'No reviews yet' }}</h4>
        <p>{{ 'products.reviews.be_first' | t | default: 'Be the first to write a review!' }}</p>
        <button class="btn btn--primary reviews-write-btn" onclick="openReviewForm()">
          {{ 'products.reviews.write_review' | t | default: 'Write a Review' }}
        </button>
      </div>
    </div>
  </div>
</div>

<style>
.product-reviews {
  background: #ffffff;
  border-radius: 16px;
  padding: 2rem;
  margin-top: 2rem;
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.reviews-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
  text-align: center;
}

.reviews-summary {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 12px;
}

.rating-display {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.rating--large .rating-star {
  font-size: 2rem;
  --letter-spacing: 0.2;
}

.rating-info {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
  font-size: 1.1rem;
}

.rating-value {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.5rem;
}

.rating-max {
  color: #64748b;
  font-size: 1.2rem;
}

.rating-count {
  color: #64748b;
  font-size: 1rem;
  margin-left: 0.5rem;
}

.reviews-widget {
  margin-top: 2rem;
}

.reviews-actions {
  margin-top: 1.5rem;
  text-align: center;
}

.reviews-fallback {
  text-align: center;
  padding: 2rem;
}

.reviews-placeholder h4 {
  font-size: 1.25rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.reviews-placeholder p {
  color: #94a3b8;
  margin-bottom: 1.5rem;
}

.reviews-write-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reviews-write-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Judge.me Widget Styling */
.jdgm-widget {
  margin: 1rem 0;
}

.jdgm-review-widget {
  border-radius: 12px;
  overflow: hidden;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .product-reviews {
    padding: 1.5rem;
    margin-top: 1.5rem;
  }
  
  .reviews-title {
    font-size: 1.5rem;
  }
  
  .rating-display {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .rating--large .rating-star {
    font-size: 1.5rem;
  }
  
  .rating-value {
    font-size: 1.25rem;
  }
}

@media screen and (max-width: 480px) {
  .product-reviews {
    padding: 1rem;
    border-radius: 12px;
  }
  
  .reviews-title {
    font-size: 1.25rem;
  }
  
  .reviews-summary {
    padding: 0.75rem;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Check if Judge.me is loaded
  function checkJudgeMeLoaded() {
    if (typeof window.jdgm !== 'undefined' && window.jdgm.widgets) {
      // Judge.me is loaded, hide fallback
      const fallback = document.querySelector('.reviews-fallback');
      if (fallback) {
        fallback.style.display = 'none';
      }
    } else {
      // Judge.me not loaded, show fallback after a delay
      setTimeout(() => {
        if (typeof window.jdgm === 'undefined' || !window.jdgm.widgets) {
          const fallback = document.querySelector('.reviews-fallback');
          if (fallback) {
            fallback.style.display = 'block';
          }
        }
      }, 3000);
    }
  }

  // Check immediately and after a delay
  checkJudgeMeLoaded();
  setTimeout(checkJudgeMeLoaded, 1000);
});

// Fallback review form function
function openReviewForm() {
  // Try to trigger Judge.me write review if available
  if (typeof window.jdgm !== 'undefined' && window.jdgm.widgets) {
    const writeButton = document.querySelector('.jdgm-write-review-button');
    if (writeButton) {
      writeButton.click();
      return;
    }
  }
  
  // Fallback: scroll to contact form or show alert
  const contactForm = document.querySelector('#contact-form, .contact-form');
  if (contactForm) {
    contactForm.scrollIntoView({ behavior: 'smooth' });
    // Pre-fill subject if possible
    const subjectField = contactForm.querySelector('input[name*="subject"], select[name*="subject"]');
    if (subjectField) {
      subjectField.value = 'Product Review - {{ product.title | escape }}';
    }
  } else {
    alert('{{ "products.reviews.contact_us" | t | default: "Please contact us to leave a review!" }}');
  }
}
</script>
